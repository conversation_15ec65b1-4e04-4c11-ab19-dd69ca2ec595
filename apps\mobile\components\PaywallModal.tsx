import React, { useState } from "react";
import { View, Modal, TouchableOpacity, ScrollView, Linking } from "react-native";
import { router } from "expo-router";
import { X, Crown, Zap, Target, ArrowRight } from "lucide-react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { useTheme } from "@react-navigation/native";
import { useAuth } from "~/lib/auth-client";

interface PaywallModalProps {
  visible: boolean;
  onClose: () => void;
  feature: string;
  featureDescription: string;
  currentPlan?: string;
  usageLimit?: {
    used: number;
    limit: number;
    resetDate?: string;
  };
}

const planBenefits = {
  foundation_plan: {
    name: "Foundation Plan",
    price: "$9.99/month",
    icon: Target,
    color: "#3b82f6",
    features: [
      "Unlimited personalized workout programs",
      "Basic form guidance and injury prevention",
      "15 grocery/fridge scans per month",
      "5 personalized weekly meal plans",
      "Standard customer support"
    ]
  },
  performance_plan: {
    name: "Performance Plan",
    price: "$19.99/month",
    icon: Zap,
    color: "#8b5cf6",
    features: [
      "Everything from Foundation Plan",
      "Advanced form correction with video tutorials",
      "Unlimited grocery/fridge scanning",
      "Custom meal plans adapted to your ingredients",
      "Priority customer support (24-hour response)"
    ]
  },
  champion_plan: {
    name: "Champion Plan",
    price: "$29.99/month",
    icon: Crown,
    color: "#f59e0b",
    features: [
      "Everything from Performance Plan",
      "AI training partner mode",
      "Advanced metabolic adaptation tracking",
      "Personalized supplement recommendations",
      "VIP customer support (same-day response)",
      "Early access to new AI features"
    ]
  }
};

export function PaywallModal({
  visible,
  onClose,
  feature,
  featureDescription,
  currentPlan,
  usageLimit
}: PaywallModalProps) {
  const { colors } = useTheme();
  const { session } = useAuth();
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  // Determine which plan to recommend based on current plan
  const getRecommendedPlan = () => {
    if (!currentPlan || currentPlan === "free") {
      return "foundation_plan";
    }
    if (currentPlan === "foundation_plan") {
      return "performance_plan";
    }
    return "champion_plan";
  };

  const recommendedPlan = getRecommendedPlan();
  const planInfo = planBenefits[recommendedPlan as keyof typeof planBenefits];
  const IconComponent = planInfo.icon;

  const handleUpgrade = async () => {
    onClose();

    // If user is logged in, redirect to web with subscription data
    if (session?.user?.email) {
      const baseUrl = "http://localhost:3000";

      // Create subscription data to pass in URL
      const subscriptionData = {
        planId: recommendedPlan,
        billing: billingCycle,
        userEmail: session.user.email,
        timestamp: Date.now(),
        fromPaywall: true
      };

      // Encode the subscription data
      const encodedData = btoa(JSON.stringify(subscriptionData));

      // Create login URL with subscription data that will redirect to Autumn checkout after login
      const loginUrl = `${baseUrl}/login?from=mobile&subscription_data=${encodedData}&action=subscribe`;

      try {
        // Open web app for subscription
        await Linking.openURL(loginUrl);
      } catch (error) {
        console.error('Error opening subscription flow:', error);
        // Fallback to pricing screen
        router.push("/(flows)/pricing");
      }
    } else {
      // User not logged in, go to pricing screen
      router.push("/(flows)/pricing");
    }
  };

  const getPaywallTitle = () => {
    if (usageLimit) {
      return `${feature} Limit Reached`;
    }
    return `Unlock ${feature}`;
  };

  const getPaywallMessage = () => {
    if (usageLimit) {
      return `You've used ${usageLimit.used} of ${usageLimit.limit} ${feature.toLowerCase()} this month. Upgrade to get unlimited access.`;
    }
    return `${featureDescription} Upgrade to unlock this premium feature.`;
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-black/50 justify-end">
        <View className="bg-background rounded-t-3xl max-h-[80%]">
          {/* Header */}
          <View className="flex-row items-center justify-between p-4 border-b border-border">
            <Text className="text-lg font-bold">{getPaywallTitle()}</Text>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView className="flex-1 p-4">
            {/* Feature Description */}
            <View className="mb-6">
              <Text className="text-center text-muted-foreground">
                {getPaywallMessage()}
              </Text>

              {usageLimit?.resetDate && (
                <Text className="text-center text-sm text-muted-foreground mt-2">
                  Your limit resets on {usageLimit.resetDate}
                </Text>
              )}
            </View>

            {/* Billing Cycle Toggle */}
            <View className="flex-row items-center justify-center mb-4 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              <TouchableOpacity
                onPress={() => setBillingCycle('monthly')}
                className={`flex-1 py-2 px-4 rounded-md ${billingCycle === 'monthly' ? 'bg-white dark:bg-gray-700' : ''}`}
              >
                <Text className={`text-center text-sm font-medium ${billingCycle === 'monthly' ? 'text-gray-900 dark:text-white' : 'text-gray-600 dark:text-gray-400'}`}>
                  Monthly
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => setBillingCycle('yearly')}
                className={`flex-1 py-2 px-4 rounded-md ${billingCycle === 'yearly' ? 'bg-white dark:bg-gray-700' : ''}`}
              >
                <Text className={`text-center text-sm font-medium ${billingCycle === 'yearly' ? 'text-gray-900 dark:text-white' : 'text-gray-600 dark:text-gray-400'}`}>
                  Yearly
                </Text>
              </TouchableOpacity>
            </View>

            {/* Recommended Plan */}
            <Card className="border-2" style={{ borderColor: planInfo.color }}>
              <CardHeader className="pb-4">
                <View className="flex-row items-center justify-between mb-2">
                  <View className="flex-row items-center">
                    <IconComponent size={24} color={planInfo.color} className="mr-2" />
                    <CardTitle className="text-lg">{planInfo.name}</CardTitle>
                  </View>
                  <Badge style={{ backgroundColor: planInfo.color }}>
                    <Text className="text-white font-medium">Recommended</Text>
                  </Badge>
                </View>

                <View className="flex-row items-baseline">
                  <Text className="text-2xl font-bold mr-1" style={{ color: planInfo.color }}>
                    {billingCycle === 'monthly' ? '$9.99' : '$101.90'}
                  </Text>
                  <Text className="text-sm text-gray-600 dark:text-gray-400">
                    /{billingCycle === 'monthly' ? 'month' : 'year'}
                  </Text>
                </View>
                {billingCycle === 'yearly' && (
                  <Text className="text-sm text-green-600 mt-1">
                    Save $17.98/year with annual billing
                  </Text>
                )}
              </CardHeader>

              <CardContent>
                <View className="space-y-2 mb-6">
                  {planInfo.features.map((feature, index) => (
                    <View key={index} className="flex-row items-start">
                      <View
                        className="w-2 h-2 rounded-full mr-3 mt-2"
                        style={{ backgroundColor: planInfo.color }}
                      />
                      <Text className="text-sm flex-1">{feature}</Text>
                    </View>
                  ))}
                </View>

                <Button
                  onPress={handleUpgrade}
                  style={{ backgroundColor: planInfo.color }}
                  className="w-full"
                >
                  <View className="flex-row items-center justify-center">
                    <Text className="text-white font-medium mr-2">
                      Start 7-Day Free Trial
                    </Text>
                    <ArrowRight size={16} color="white" />
                  </View>
                </Button>
              </CardContent>
            </Card>

            {/* Alternative Plans */}
            <TouchableOpacity
              onPress={handleUpgrade}
              className="mt-4 p-4 border border-border rounded-lg"
            >
              <Text className="text-center text-primary font-medium">
                View All Plans & Pricing
              </Text>
            </TouchableOpacity>

            {/* Footer */}
            <View className="mt-6 mb-4">
              <Text className="text-center text-xs text-muted-foreground">
                All plans include a 7-day free trial. Cancel anytime.
              </Text>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}
