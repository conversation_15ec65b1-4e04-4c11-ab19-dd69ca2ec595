import { createFileRoute, useSearch } from '@tanstack/react-router'
import LoginPageWithRedirect from '@/components/auth/login-page-with-redirect'
import { useEffect } from 'react'
import { useSession } from '@/lib/auth-client'
import { useAutumn } from 'autumn-js/react'

export const Route = createFileRoute('/login')({
  component: LoginPage,
})

function LoginPage() {
  const searchParams = useSearch({ from: '/login' }) as any
  const { data: session } = useSession()
  const { attach } = useAutumn()

  // Handle subscription flow after login
  useEffect(() => {
    const handleSubscriptionFlow = async () => {
      if (session?.user && searchParams?.subscription_data && searchParams?.action === 'subscribe') {
        try {
          // Decode subscription data
          const subscriptionData = JSON.parse(atob(searchParams.subscription_data))

          // Verify user email matches
          if (subscriptionData.userEmail === session.user.email) {
            console.log('Starting Autumn checkout for:', subscriptionData.planId)

            // Redirect directly to Autumn Stripe checkout
            await attach({
              productId: subscriptionData.planId,
              metadata: {
                billing_cycle: subscriptionData.billing || 'monthly',
                from_mobile: 'true',
                from_onboarding: subscriptionData.fromOnboarding ? 'true' : 'false',
                user_email: session.user.email,
                success_url: 'mobile://subscription-success',
                cancel_url: 'mobile://subscription-cancelled'
              }
            })
          } else {
            console.error('Email mismatch in subscription flow')
            // Redirect to account page if email doesn't match
            window.location.href = '/account'
          }
        } catch (error) {
          console.error('Error processing subscription:', error)
          // Redirect to account page on error
          window.location.href = '/account'
        }
      }
    }

    handleSubscriptionFlow()
  }, [session, searchParams, attach])

  return (
    <LoginPageWithRedirect
      searchParams={searchParams}
    />
  )
}
