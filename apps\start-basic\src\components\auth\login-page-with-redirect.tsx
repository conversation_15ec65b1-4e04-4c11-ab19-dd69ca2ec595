import { useEffect } from 'react'
import { useRouter, useSearch } from '@tanstack/react-router'
import { useSession } from '@/lib/auth-client'
import { useAutumn } from 'autumn-js/react'
import LoginPage from '@/components/auth/login-page'
import { parseMobilePlanSelection, storePlanSelectionForAfterLogin } from '@/lib/mobile-web-bridge'

export default function LoginPageWithRedirect() {
  const { data: session, isPending } = useSession()
  const { attach } = useAutumn()
  const router = useRouter()
  const searchParams = useSearch({ from: '/login' }) as any

  useEffect(() => {
    // Check for mobile plan data in URL
    const urlParams = new URLSearchParams(window.location.search)
    const planSelection = parseMobilePlanSelection(urlParams)

    if (planSelection) {
      // Store plan selection for after login
      storePlanSelectionForAfterLogin(planSelection)
      console.log('Stored mobile plan selection:', planSelection)
    }

    // If we have a session and it's not pending, handle redirect
    if (session?.user && !isPending) {
      // Check if we have a stored plan selection from mobile
      const storedPlan = sessionStorage.getItem('pending_mobile_plan')

      if (storedPlan) {
        try {
          const planData = JSON.parse(storedPlan)

          // Verify the email matches (security check)
          if (planData.userEmail === session.user.email) {
            // Clear the stored plan
            sessionStorage.removeItem('pending_mobile_plan')

            // Use Autumn's attach function to redirect directly to Stripe checkout
            attach({
              productId: planData.planId,
              metadata: {
                billing_cycle: planData.billing,
                from_mobile: true,
                user_email: planData.userEmail
              }
            }).catch((error) => {
              console.error('Error starting subscription from mobile:', error)
              // Fallback to account page
              router.navigate({ to: '/account' })
            })
            return
          } else {
            console.warn('Email mismatch between stored plan and logged in user')
            sessionStorage.removeItem('pending_mobile_plan')
          }
        } catch (error) {
          console.error('Error parsing stored plan data:', error)
          sessionStorage.removeItem('pending_mobile_plan')
        }
      }

      // Default redirect to account page
      router.navigate({ to: '/account' })
    }
  }, [session, isPending, router, attach])

  // Show loading state while checking session
  if (isPending) {
    return (
      <div className="min-h-screen  bg-[#e9e5dc] dark:bg-[#1e1b16]  flex items-center justify-center  p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // If user is authenticated, don't render the login page (redirect will happen)
  if (session?.user) {
    return null
  }

  // If not authenticated, show the login page
  return <LoginPage />
}
