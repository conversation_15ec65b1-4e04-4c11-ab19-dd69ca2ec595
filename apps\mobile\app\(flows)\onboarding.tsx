import React, { useEffect, useState } from "react";
import { View, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { OnboardingFlow } from "~/components/OnboardingFlow";
import { router } from "expo-router";
import * as SecureStore from "expo-secure-store";
import { PREVENT_REDIRECT_KEY } from "~/lib/utils";
import { Text } from "~/components/ui/text";

export default function OnboardingScreen() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Set the prevent redirect flag when onboarding starts
    const setRedirectFlag = async () => {
      try {
        await SecureStore.setItemAsync(PREVENT_REDIRECT_KEY, "true");
        setIsLoading(false);
      } catch (error) {
        console.error("[Onboarding] Error setting redirect flag:", error);
        setIsLoading(false);
      }
    };

    setRedirectFlag();
  }, []);

  const handleOnboardingComplete = async () => {
    try {
      // Clear the prevent redirect flag
      await SecureStore.deleteItemAsync(PREVENT_REDIRECT_KEY);

      // Navigate to main tabs after onboarding is complete
      router.replace("/(tabs)/dashboard");
    } catch (error) {
      console.error("[Onboarding] Error completing onboarding:", error);
      // Still navigate even if flag clearing fails
      router.replace("/(tabs)/dashboard");
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-background">
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" />
          <Text className="mt-2">Setting up onboarding...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background">
      <View className="flex-1">
        <OnboardingFlow onComplete={handleOnboardingComplete} />
      </View>
    </SafeAreaView>
  );
}
