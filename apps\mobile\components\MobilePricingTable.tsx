import React, { useState } from "react";
import { <PERSON>, ScrollView, <PERSON><PERSON>, Al<PERSON> } from "react-native";
import { Crown, Zap, Target, Check } from "lucide-react-native";
import { Text } from "~/components/ui/text";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { useTheme } from "@react-navigation/native";
import { useAuth } from "~/lib/auth-client";

interface MobilePricingTableProps {
  onPlanSelect?: (planId: string, billing: 'monthly' | 'yearly') => void;
  showTrialMessaging?: boolean;
  fromOnboarding?: boolean;
}

// Plan configurations matching your Autumn setup
const planConfigs = {
  'foundation_plan': {
    name: 'Foundation Plan',
    icon: Target,
    color: '#3b82f6',
    popular: false,
    description: 'Build your fitness foundation with intelligent AI guidance',
    monthlyPrice: 9.99,
    yearlyPrice: 101.90,
    features: [
      'Unlimited workout plans',
      '15 meal scans per month',
      'Form guidance with AI feedback',
      '3 progress photos per month',
      '5 meal planning sessions per month'
    ]
  },
  'performance_plan': {
    name: 'Performance Plan',
    icon: Zap,
    color: '#10b981',
    popular: true,
    description: 'Optimize your performance with advanced AI coaching',
    monthlyPrice: 19.99,
    yearlyPrice: 203.90,
    features: [
      'Everything in Foundation',
      '50 meal scans per month',
      'Advanced form analysis',
      '10 progress photos per month',
      '15 meal planning sessions per month',
      'Workout intensity optimization',
      'Recovery recommendations'
    ]
  },
  'champion_plan': {
    name: 'Champion Plan',
    icon: Crown,
    color: '#f59e0b',
    popular: false,
    description: 'Elite-level training with unlimited AI coaching',
    monthlyPrice: 29.99,
    yearlyPrice: 305.90,
    features: [
      'Everything in Performance',
      'Unlimited meal scans',
      'Real-time form correction',
      'Unlimited progress photos',
      'Unlimited meal planning',
      'Personalized nutrition coaching',
      'Priority support'
    ]
  }
};

export function MobilePricingTable({ 
  onPlanSelect, 
  showTrialMessaging = true,
  fromOnboarding = false 
}: MobilePricingTableProps) {
  const { colors } = useTheme();
  const { session } = useAuth();
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [isLoading, setIsLoading] = useState(false);

  const handlePlanSelection = async (planId: string) => {
    if (!session?.user?.email) {
      Alert.alert("Error", "Please log in to continue");
      return;
    }

    setIsLoading(true);

    try {
      if (onPlanSelect) {
        // Use custom handler if provided
        onPlanSelect(planId, billingCycle);
      } else {
        // Default behavior: redirect to web app
        const baseUrl = "http://localhost:3000";

        const subscriptionData = {
          planId,
          billing: billingCycle,
          userEmail: session.user.email,
          timestamp: Date.now(),
          fromOnboarding
        };

        const encodedData = btoa(JSON.stringify(subscriptionData));
        const loginUrl = `${baseUrl}/login?from=mobile&subscription_data=${encodedData}&action=subscribe`;

        const canOpen = await Linking.canOpenURL(loginUrl);
        if (canOpen) {
          await Linking.openURL(loginUrl);
        } else {
          Alert.alert("Error", "Unable to open payment page");
        }
      }
    } catch (error) {
      console.error("Plan selection error:", error);
      Alert.alert("Error", "Failed to process plan selection");
    } finally {
      setIsLoading(false);
    }
  };

  const getButtonText = () => {
    if (showTrialMessaging) {
      return 'Start 7-Day Free Trial';
    } else {
      return 'Subscribe Now';
    }
  };

  const calculateYearlySavings = (monthlyPrice: number, yearlyPrice: number) => {
    return ((monthlyPrice * 12) - yearlyPrice).toFixed(0);
  };

  return (
    <ScrollView className="flex-1 bg-[#e9e5dc] dark:bg-[#1e1b16]">
      <View className="p-6">
        {/* Header */}
        <View className="mb-8">
          <Text className="text-3xl font-bold text-center text-gray-900 dark:text-white font-manrope_1 mb-4">
            Choose Your Plan
          </Text>
          {showTrialMessaging && (
            <Text className="text-lg text-center text-[#7e7b76] font-manrope_1">
              Start your 7-day free trial and unlock AI-powered fitness coaching
            </Text>
          )}
        </View>

        {/* Billing Cycle Toggle */}
        <View className="flex-row items-center justify-center mb-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
          <Button
            variant={billingCycle === 'monthly' ? 'default' : 'ghost'}
            onPress={() => setBillingCycle('monthly')}
            className="mr-2"
          >
            <Text className={`font-manrope_1 ${billingCycle === 'monthly' ? 'text-white' : 'text-gray-600 dark:text-gray-400'}`}>
              Monthly
            </Text>
          </Button>
          <Button
            variant={billingCycle === 'yearly' ? 'default' : 'ghost'}
            onPress={() => setBillingCycle('yearly')}
            className="ml-2"
          >
            <Text className={`font-manrope_1 ${billingCycle === 'yearly' ? 'text-white' : 'text-gray-600 dark:text-gray-400'}`}>
              Yearly
            </Text>
          </Button>
          {billingCycle === 'yearly' && (
            <Badge className="ml-2 bg-green-100 dark:bg-green-900">
              <Text className="text-green-700 dark:text-green-300 text-xs font-manrope_1">
                Save 15%
              </Text>
            </Badge>
          )}
        </View>

        {/* Plans */}
        <View className="space-y-4">
          {Object.entries(planConfigs).map(([planId, config]) => {
            const IconComponent = config.icon;
            const price = billingCycle === 'monthly' ? config.monthlyPrice : config.yearlyPrice;
            const displayPrice = billingCycle === 'monthly' ? price : (price / 12);

            return (
              <Card
                key={planId}
                className={`${config.popular ? 'border-primary border-2' : 'border-gray-200 dark:border-gray-600'}`}
              >
                {config.popular && (
                  <View className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                    <Badge className="bg-primary">
                      <Text className="text-primary-foreground font-manrope_1">
                        Most Popular
                      </Text>
                    </Badge>
                  </View>
                )}

                <CardHeader className="pb-4">
                  <View className="flex-row items-center mb-2">
                    <IconComponent size={24} color={config.color} />
                    <CardTitle className="text-lg font-manrope_1 ml-2">{config.name}</CardTitle>
                  </View>

                  <View className="flex-row items-baseline mb-2">
                    <Text className="text-3xl font-bold text-gray-900 dark:text-white font-manrope_1">
                      ${displayPrice.toFixed(2)}
                    </Text>
                    <Text className="text-[#7e7b76] ml-1 font-manrope_1">/month</Text>
                  </View>

                  {billingCycle === 'yearly' && (
                    <Text className="text-sm text-green-600 font-manrope_1">
                      Save ${calculateYearlySavings(config.monthlyPrice, config.yearlyPrice)}/year with annual billing
                    </Text>
                  )}

                  <Text className="text-[#7e7b76] text-sm font-manrope_1">
                    {config.description}
                  </Text>
                </CardHeader>

                <CardContent>
                  <View className="space-y-3 mb-6">
                    {config.features.map((feature, index) => (
                      <View key={index} className="flex-row items-start">
                        <Check size={16} color="#22c55e" className="mr-2 mt-0.5" />
                        <Text className="text-sm text-gray-700 dark:text-gray-300 font-manrope_1 flex-1">
                          {feature}
                        </Text>
                      </View>
                    ))}
                  </View>

                  <Button
                    onPress={() => handlePlanSelection(planId)}
                    disabled={isLoading}
                    className={`w-full font-manrope_1 ${config.popular ? 'bg-primary' : ''}`}
                    style={!config.popular ? { backgroundColor: config.color } : undefined}
                  >
                    <Text className="text-white font-manrope_1">
                      {isLoading ? 'Processing...' : getButtonText()}
                    </Text>
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </View>
      </View>
    </ScrollView>
  );
}
